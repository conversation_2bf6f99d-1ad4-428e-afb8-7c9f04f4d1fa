# Hướng Dẫn Chạy Tests

## Tóm Tắt Vấn Đề Đã Khắc <PERSON>

**Lỗi ban đầu:** `UnsatisfiedLinkError` khi chạy test ONNX Runtime
**Nguyên nhân:** Unit test không thể load native libraries của ONNX Runtime
**Giải pháp:** Tách thành 2 loại test riêng biệt

## Các Loại Test

### 1. Unit Test (Đã Fix) ✅
**File:** `app/src/test/java/.../ModelQualityTest.kt`
**M<PERSON><PERSON> đích:** Test logic cơ bản không cần ONNX Runtime
**Chạy:** 
```bash
./gradlew testDebugUnitTest
```

**Nội dung test:**
- Validation logic cho bounding boxes
- Tính toán confidence scores
- Kiểm tra class IDs
- Không cần Android framework hay native libraries

### 2. Instrumented Test (Cho ONNX Runtime) 🔄
**File:** `app/src/androidTest/java/.../ModelQualityInstrumentedTest.kt`
**M<PERSON><PERSON> đích:** Test ONNX Runtime với native libraries
**Chạy:**
```bash
# Cần kết nối thiết bị Android hoặc emulator
./gradlew connectedAndroidTest
```

**Nội dung test:**
- Load model ONNX từ assets
- Chạy inference thực tế
- Đo thời gian inference
- Đánh giá chất lượng model
- Tính toán metrics chi tiết

## Cách Chạy Tests

### Chạy Unit Test (Nhanh)
```bash
# Chạy tất cả unit tests
./gradlew testDebugUnitTest

# Chạy test cụ thể
./gradlew testDebugUnitTest --tests "ai.onnxruntime.example.objectdetection.ModelQualityTest.testBasicLogic"
```

### Chạy Instrumented Test (Cần thiết bị)
```bash
# 1. Kết nối thiết bị Android hoặc khởi động emulator
# 2. Chạy test
./gradlew connectedAndroidTest

# Hoặc chạy test cụ thể
./gradlew connectedAndroidTest -Pandroid.testInstrumentationRunnerArguments.class=ai.onnxruntime.example.objectdetection.ModelQualityInstrumentedTest
```

## Kết Quả Mong Đợi

### Unit Test
```
BUILD SUCCESSFUL
1 test completed
```

### Instrumented Test
```
=== BẮT ĐẦU TEST CHẤT LƯỢNG MODEL (INSTRUMENTED) ===
✓ Đã load model thành công (X bytes)
✓ Đã khởi tạo ONNX session thành công
✓ Đã load ảnh test thành công

=== KẾT QUẢ DETECTION ===
Thời gian inference: Xms
Số objects phát hiện: X

=== ĐÁNH GIÁ CHẤT LƯỢNG MODEL ===
✓ Model phát hiện được X objects
✓ Thời gian inference hợp lý: Xms
✓ Có X/X objects với confidence >= 0.5
✓ Tất cả X bounding boxes đều hợp lệ

=== METRICS CHI TIẾT ===
Confidence trung bình: X.XXX
Confidence cao nhất: X.XXX
Confidence thấp nhất: X.XXX
...
```

## Lưu Ý

1. **Unit Test:** Chạy nhanh, không cần thiết bị, test logic cơ bản
2. **Instrumented Test:** Chạy chậm hơn, cần thiết bị Android, test ONNX Runtime thực tế
3. **Để test đầy đủ:** Chạy cả 2 loại test
4. **CI/CD:** Unit test có thể chạy trên CI, Instrumented test cần emulator

## Troubleshooting

### Nếu Unit Test Fail
- Kiểm tra logic validation
- Xem log output để debug

### Nếu Instrumented Test Fail
- Đảm bảo thiết bị/emulator đã kết nối: `adb devices`
- Kiểm tra model file trong assets: `app/src/main/assets/best.onnx`
- Kiểm tra ảnh test trong assets: `app/src/main/assets/img.png`
- Xem log chi tiết: `./gradlew connectedAndroidTest --info`
