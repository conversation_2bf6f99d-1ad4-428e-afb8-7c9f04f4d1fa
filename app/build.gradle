plugins {
    id 'com.android.application'
    id 'org.jetbrains.kotlin.android'
    id 'kotlin-parcelize'
}

android {
    namespace 'ai.onnxruntime.example.objectdetection'
    compileSdk 32

    defaultConfig {
        applicationId "ai.onnxruntime.example.objectdetection"
        minSdk 24
        targetSdk 32
        versionCode 1
        versionName "1.0"

        testInstrumentationRunner "androidx.test.runner.AndroidJUnitRunner"
    }

    buildTypes {
        release {
            minifyEnabled false
            proguardFiles getDefaultProguardFile('proguard-android-optimize.txt'), 'proguard-rules.pro'
        }
    }
    compileOptions {
        sourceCompatibility JavaVersion.VERSION_1_8
        targetCompatibility JavaVersion.VERSION_1_8
    }
    kotlinOptions {
        jvmTarget = '1.8'
    }
    buildFeatures {
        viewBinding true
    }
}

dependencies {
    implementation 'androidx.core:core-ktx:1.7.0'
    implementation 'androidx.appcompat:appcompat:1.5.1'
    implementation 'com.google.android.material:material:1.7.0'
    implementation 'androidx.constraintlayout:constraintlayout:2.1.4'

    implementation 'androidx.test:monitor:1.4.0'
    testImplementation 'junit:junit:4.13.2'
    androidTestImplementation 'androidx.test.ext:junit:1.1.4'
    androidTestImplementation 'androidx.test.espresso:espresso-core:3.5.0'

    // ONNX Runtime cho desktop testing (không cần Android)
//    testImplementation 'com.microsoft.onnxruntime:onnxruntime:1.16.3'
//    implementation 'com.microsoft.onnxruntime:onnxruntime:latest.release'

    implementation 'com.microsoft.onnxruntime:onnxruntime-android:latest.release'
    implementation 'com.microsoft.onnxruntime:onnxruntime-extensions-android:latest.release'
}