package ai.onnxruntime.example.objectdetection

import ai.onnxruntime.*
import android.graphics.Bitmap
import android.util.Log
import java.nio.FloatBuffer

class OCRModel(private val session: OrtSession) {
    fun runOCR(bitmap: Bitmap): IntArray {
        val inputName = session.inputNames.first()
        val tensor = preprocess(bitmap)

        val start = System.nanoTime()
        val result = session.run(mapOf(inputName to tensor))
        val end = System.nanoTime()

        val latencyMs = (end - start) / 1_000_000.0
        Log.d("OCRModel", "Latency: $latencyMs ms")
        val outputValue = result[0].value

        Log.d("OCRModel", "outputValue: $outputValue")

        return when (outputValue) {
            is Array<*> -> {
                when {
                    outputValue.isNotEmpty() && outputValue[0] is LongArray -> {
                        val longArrays = outputValue as Array<LongArray>
                        longArrays[0].map { it.toInt() }.toIntArray()   // ✅ convert long → int
                    }
                    outputValue.isNotEmpty() && outputValue[0] is IntArray -> {
                        val intArrays = outputValue as Array<IntArray>
                        intArrays[0]
                    }
                    else -> throw IllegalStateException("Unsupported array type: ${outputValue[0]?.javaClass}")
                }
            }
            is LongArray -> outputValue.map { it.toInt() }.toIntArray()   // ✅ convert long → int
            is IntArray -> outputValue
            else -> throw IllegalStateException("Unsupported output type: ${outputValue?.javaClass}")
        }
    }


    private fun preprocess(bitmap: Bitmap): OnnxTensor {
        val env = OrtEnvironment.getEnvironment()  // ✅ lấy environment ở đây

        val w = bitmap.width
        val h = bitmap.height
        val buf = FloatBuffer.allocate(1 * h * w * 3)

        val pixels = IntArray(w * h)
        bitmap.getPixels(pixels, 0, w, 0, 0, w, h)

        for (p in pixels) {
            buf.put((p shr 16 and 0xFF) / 255f) // R
            buf.put((p shr 8 and 0xFF) / 255f)  // G
            buf.put((p and 0xFF) / 255f)        // B
        }
        buf.rewind()

        val shape = longArrayOf(1, h.toLong(), w.toLong(), 3)
        return OnnxTensor.createTensor(env, buf, shape)
    }
}
