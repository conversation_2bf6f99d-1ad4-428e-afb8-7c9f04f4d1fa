package ai.onnxruntime.example.objectdetection

import android.util.Log
import org.json.JSONObject
import java.io.File

class Vocab(private val idx2char: Map<Int, String>) {
    fun decode(ids: List<Int>): String {
        val goIdx = idx2char.entries.find { it.value == "<SOS>" }?.key
        val eosIdx = idx2char.entries.find { it.value == "<EOS>" }?.key

        val first = if (goIdx != null && ids.contains(goIdx)) 1 else 0
        val lastIndex = if (eosIdx != null && ids.contains(eosIdx)) ids.indexOf(eosIdx) else ids.size

        val result = ids.subList(first, lastIndex)
            .mapNotNull { idx2char[it] }
            .filter { it != "<PAD>" && it != "<SOS>" && it != "<EOS>" }
            .joinToString("")

        return result
    }

    companion object {
        fun loadFromFile(file: File): Vocab {
            val jsonStr = file.readText()
            val jsonObj = JSONObject(jsonStr)

            val map = mutableMapOf<Int, String>()
            val keys = jsonObj.keys()
            while (keys.hasNext()) {
                val k = keys.next()
                map[k.toInt()] = jsonObj.getString(k)
            }
            return Vocab(map)
        }
    }
}
