package ai.onnxruntime.example.objectdetection

import android.graphics.BitmapFactory
import androidx.appcompat.app.AppCompatActivity
import android.os.Bundle
import android.widget.Button
import android.widget.ImageView
import android.widget.TextView
import android.util.Log
import android.widget.Toast
import ai.onnxruntime.OrtEnvironment
import ai.onnxruntime.OrtSession
import java.io.File
import java.io.FileOutputStream

class MainActivity : AppCompatActivity() {
    private var ocrModel: OCRModel? = null
    private var vocab: Vocab? = null

    private lateinit var imageView: ImageView
    private lateinit var textView: TextView

    private var imageList = listOf("img_0.png", "img_1.png", "img_2.png")
    private var currentIndex = 0
    private var isModelLoaded = false

    companion object {
        private const val TAG = "MainActivity"
    }

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        setContentView(R.layout.activity_main)

        imageView = findViewById(R.id.imageView)
        textView = findViewById(R.id.resultText)
        val btnNext = findViewById<Button>(R.id.btnNext)
        val btnPrev = findViewById<Button>(R.id.btnPrev)

        initializeModel()

        btnNext.setOnClickListener {
            if (isModelLoaded) {
                currentIndex = (currentIndex + 1) % imageList.size
                showImageAndRunOCR()
            } else {
                showError("Model chưa được load. Vui lòng khởi động lại app.")
            }
        }

        btnPrev.setOnClickListener {
            if (isModelLoaded) {
                currentIndex = if (currentIndex - 1 < 0) imageList.size - 1 else currentIndex - 1
                showImageAndRunOCR()
            } else {
                showError("Model chưa được load. Vui lòng khởi động lại app.")
            }
        }
    }

    private fun initializeModel() {
        try {
            Log.d(TAG, "Bắt đầu khởi tạo model...")
            textView.text = "Đang load model..."

            val env = OrtEnvironment.getEnvironment()

            // Copy model từ raw ra cacheDir
            val modelFile = File(cacheDir, "full_ocr.onnx")
            if (!modelFile.exists()) {
                resources.openRawResource(R.raw.full_ocr).use { input ->
                    FileOutputStream(modelFile).use { output ->
                        input.copyTo(output)
                    }
                }
            }

            val session = env.createSession(modelFile.absolutePath, OrtSession.SessionOptions())
            ocrModel = OCRModel(session)

            // Copy vocab từ raw ra cacheDir
            val vocabFile = File(cacheDir, "vocab.json")
            if (!vocabFile.exists()) {
                resources.openRawResource(R.raw.vocab).use { input ->
                    FileOutputStream(vocabFile).use { output ->
                        input.copyTo(output)
                    }
                }
            }
            vocab = Vocab.loadFromFile(vocabFile)

            isModelLoaded = true
            textView.text = "Model đã sẵn sàng!"

            showImageAndRunOCR()

        } catch (e: Exception) {
            Log.e(TAG, "Lỗi khởi tạo model: ${e.message}", e)
            isModelLoaded = false
            showError("Lỗi khởi tạo model: ${e.message}")
        }
    }

    private fun showImageAndRunOCR() {
        if (!isModelLoaded || ocrModel == null || vocab == null) {
            showError("Model chưa sẵn sàng")
            return
        }

        try {
            val imgName = imageList[currentIndex]
            Log.d(TAG, "Đang xử lý ảnh: $imgName")

            val inputStream = assets.open(imgName) // ảnh vẫn trong assets
            val bitmap = BitmapFactory.decodeStream(inputStream)
            inputStream.close()

            if (bitmap == null) {
                showError("Không thể load ảnh: $imgName")
                return
            }

            imageView.setImageBitmap(bitmap)
            textView.text = "Đang xử lý ảnh $imgName..."

            val tokens = ocrModel!!.runOCR(bitmap)
            Log.d(TAG, "OCR hoàn thành: $tokens")
            val text = vocab!!.decode(tokens.asIterable() as List<Int>)

            textView.text = "Ảnh: $imgName\nKết quả OCR: $text"
            Log.d(TAG, "OCR hoàn thành: $text")

        } catch (e: Exception) {
            Log.e(TAG, "Lỗi khi xử lý ảnh: ${e.message}", e)
            showError("Lỗi xử lý ảnh: ${e.message}")
        }
    }

    private fun showError(message: String) {
        Log.e(TAG, message)
        textView.text = "❌ $message"
        Toast.makeText(this, message, Toast.LENGTH_LONG).show()
    }
}
